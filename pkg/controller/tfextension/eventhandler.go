/*
 Copyright 2021 The Kube-Queue Authors.

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/

package tfextension

import (
	"context"
	"fmt"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"

	"github.com/kube-queue/api/pkg/apis/scheduling/v1alpha1"
	commonv1 "github.com/kubeflow/common/pkg/apis/common/v1"
	tfjobv1 "github.com/kubeflow/training-operator/pkg/apis/kubeflow.org/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	// Suspend is a flag annotation for tfjob to use the queueunit crd
	Suspend               = "scheduling.x-k8s.io/suspend"
	Queuing               = "Queuing"
	ConsumerRefKind       = "TFJob"
	ConsumerRefAPIVersion = "kubeflow.org/v1"
	// QuNameSuffix is the suffix of the queue unit name when create a new one.
	QuNameSuffix           = "-tf-qu"
	OptimisticLockErrorMsg = "the object has been modified; please apply your changes to the latest version and try again"
)

func (c *Controller) addAllEventHandlers() {
	c.queueInformer.Informer().AddEventHandler(
		cache.FilteringResourceEventHandler{
			FilterFunc: func(obj interface{}) bool {
				switch qu := obj.(type) {
				case *v1alpha1.QueueUnit:
					if qu.Spec.ConsumerRef != nil &&
						qu.Spec.ConsumerRef.Kind == ConsumerRefKind &&
						qu.Spec.ConsumerRef.APIVersion == ConsumerRefAPIVersion {
						return true
					}
					return false
				default:
					return false
				}
			},
			Handler: cache.ResourceEventHandlerFuncs{
				AddFunc:    c.AddQueueUnit,
				UpdateFunc: c.UpdateQueueUnit,
				DeleteFunc: c.DeleteQueueUnit,
			},
		},
	)

	c.tfjobInformer.Informer().AddEventHandler(
		cache.FilteringResourceEventHandler{
			FilterFunc: func(obj interface{}) bool {
				switch obj.(type) {
				case *tfjobv1.TFJob:
					return true
				default:
					return false
				}
			},
			Handler: cache.ResourceEventHandlerFuncs{
				AddFunc:    c.AddTFJob,
				UpdateFunc: c.UpdateTFJob,
				DeleteFunc: c.DeleteTFJob,
			},
		},
	)
}

func (c *Controller) syncHandler(key string) error {
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		runtime.HandleError(fmt.Errorf("invalid resource key: %s", key))
		return err
	}

	queueUnit, err := c.queueInformer.Lister().QueueUnits(namespace).Get(name)
	if err != nil {
		if errors.IsNotFound(err) {
			klog.V(4).Infof("QueueUnit %v/%v not found, maybe deleted", namespace, name)
			return nil
		}
		return err
	}

	klog.V(4).Infof("Processing QueueUnit %v/%v", queueUnit.Namespace, queueUnit.Name)

	if queueUnit.Status.Phase == v1alpha1.Dequeued {
		klog.Infof("QueueUnit %v/%v has dequeued, removing suspend annotation from TFJob", queueUnit.Namespace, queueUnit.Name)
		return c.removeSuspendAnnotation(queueUnit)
	}

	return nil
}

func (c *Controller) enqueueQueueUnit(obj interface{}) {
	var key string
	var err error
	if key, err = cache.MetaNamespaceKeyFunc(obj); err != nil {
		runtime.HandleError(err)
		return
	}
	c.workqueue.AddRateLimited(key)
}

func (c *Controller) AddQueueUnit(obj interface{}) {
	qu := obj.(*v1alpha1.QueueUnit)
	klog.V(4).Infof("AddQueueUnit: %v/%v", qu.Namespace, qu.Name)
	c.enqueueQueueUnit(qu)
}

func (c *Controller) UpdateQueueUnit(oldObj, newObj interface{}) {
	oldQu := oldObj.(*v1alpha1.QueueUnit)
	newQu := newObj.(*v1alpha1.QueueUnit)
	if oldQu.ResourceVersion == newQu.ResourceVersion {
		return
	}
	klog.V(4).Infof("UpdateQueueUnit: %v/%v", newQu.Namespace, newQu.Name)
	c.enqueueQueueUnit(newQu)
}

func (c *Controller) DeleteQueueUnit(obj interface{}) {
	qu := obj.(*v1alpha1.QueueUnit)
	klog.V(4).Infof("DeleteQueueUnit: %v/%v", qu.Namespace, qu.Name)
}

func (c *Controller) AddTFJob(obj interface{}) {
	tfJob := obj.(*tfjobv1.TFJob)
	klog.V(4).Infof("AddTFJob: %v/%v", tfJob.Namespace, tfJob.Name)

	if err := c.createQueueUnitForTFJob(tfJob); err != nil {
		klog.Errorf("Failed to create QueueUnit for TFJob %v/%v: %v", tfJob.Namespace, tfJob.Name, err)
		return
	}

	if err := c.updateTFJobStatus(tfJob); err != nil {
		klog.Errorf("Failed to update TFJob status %v/%v: %v", tfJob.Namespace, tfJob.Name, err)
	}
}

func (c *Controller) UpdateTFJob(oldObj, newObj interface{}) {
	newTfJob := newObj.(*tfjobv1.TFJob)
	conditionsLen := len(newTfJob.Status.Conditions)
	if conditionsLen > 0 {
		lastCondition := newTfJob.Status.Conditions[conditionsLen-1]
		if lastCondition.Type == commonv1.JobFailed || lastCondition.Type == commonv1.JobSucceeded {
			klog.Infof("TFJob %v/%v finished with status: %v", newTfJob.Namespace, newTfJob.Name, lastCondition.Type)
			c.deleteQueueUnitForTFJob(newTfJob)
		}
	}
}

func (c *Controller) DeleteTFJob(obj interface{}) {
	tfJob := obj.(*tfjobv1.TFJob)
	klog.V(4).Infof("DeleteTFJob: %v/%v", tfJob.Namespace, tfJob.Name)
	c.deleteQueueUnitForTFJob(tfJob)
}

func (c *Controller) createQueueUnitForTFJob(tfJob *tfjobv1.TFJob) error {
	// Check if TFJob has suspend annotation
	_, hasSuspend := tfJob.Annotations[Suspend]
	if !hasSuspend {
		klog.V(4).Infof("TFJob %v/%v does not have suspend annotation, skipping", tfJob.Namespace, tfJob.Name)
		return nil
	}

	// Check if QueueUnit already exists
	quName := tfJob.Name + QuNameSuffix
	qu, err := c.queueInformer.Lister().QueueUnits(tfJob.Namespace).Get(quName)
	if err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
		// QueueUnit doesn't exist, create it
		klog.Infof("Creating QueueUnit for TFJob %v/%v", tfJob.Namespace, tfJob.Name)
		queueUnit := c.buildQueueUnit(tfJob)
		_, err = c.queueClient.SchedulingV1alpha1().QueueUnits(queueUnit.Namespace).Create(context.TODO(), queueUnit, metav1.CreateOptions{})
		if err != nil {
			return fmt.Errorf("failed to create QueueUnit: %v", err)
		}
		klog.Infof("Successfully created QueueUnit %v/%v", queueUnit.Namespace, queueUnit.Name)
		return nil
	}

	// QueueUnit already exists
	if qu.Spec.ConsumerRef.Kind == ConsumerRefKind {
		klog.V(4).Infof("QueueUnit %v/%v already exists for TFJob %v/%v", qu.Namespace, qu.Name, tfJob.Namespace, tfJob.Name)
	} else {
		klog.Warningf("Unexpected QueueUnit %v/%v exists for TFJob %v/%v", qu.Namespace, qu.Name, tfJob.Namespace, tfJob.Name)
	}

	return nil
}

func (c *Controller) buildQueueUnit(tfJob *tfjobv1.TFJob) *v1alpha1.QueueUnit {
	// Build ObjectReference
	objectRef := &corev1.ObjectReference{
		APIVersion: ConsumerRefAPIVersion,
		Kind:       ConsumerRefKind,
		Namespace:  tfJob.Namespace,
		Name:       tfJob.Name,
	}

	// Get priority information
	var priorityClassName string
	var priority *int32
	for _, replicaSpec := range tfJob.Spec.TFReplicaSpecs {
		priorityClassName = replicaSpec.Template.Spec.PriorityClassName
		priority = replicaSpec.Template.Spec.Priority
		break // Assume all replicas have the same priority
	}

	// Get priority from PriorityClass if exists
	if priorityClassName != "" {
		priorityClass, err := c.kubeClient.SchedulingV1().PriorityClasses().Get(context.TODO(), priorityClassName, metav1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				klog.Infof("PriorityClass %v not found", priorityClassName)
			} else {
				klog.Errorf("Failed to get PriorityClass %v: %v", priorityClassName, err)
			}
		} else {
			priority = &priorityClass.Value
		}
	}

	// Calculate total resources
	resources := c.calculateTotalResources(tfJob)

	return &v1alpha1.QueueUnit{
		ObjectMeta: metav1.ObjectMeta{
			Name:      tfJob.Name + QuNameSuffix,
			Namespace: tfJob.Namespace,
		},
		Spec: v1alpha1.QueueUnitSpec{
			ConsumerRef:       objectRef,
			Priority:          priority,
			PriorityClassName: priorityClassName,
			Resource:          resources,
		},
		Status: v1alpha1.QueueUnitStatus{
			Phase:   v1alpha1.Enqueued,
			Message: "QueueUnit enqueued after TFJob creation",
		},
	}
}

func (c *Controller) calculateTotalResources(tfJob *tfjobv1.TFJob) corev1.ResourceList {
	totalResources := corev1.ResourceList{}

	for _, replicaSpec := range tfJob.Spec.TFReplicaSpecs {
		replicas := int(*replicaSpec.Replicas)
		for _, container := range replicaSpec.Template.Spec.Containers {
			for resourceType, quantity := range container.Resources.Requests {
				replicaQuantity := resource.Quantity{}
				for i := 0; i < replicas; i++ {
					replicaQuantity.Add(quantity)
				}

				if totalQuantity, exists := totalResources[resourceType]; exists {
					totalQuantity.Add(replicaQuantity)
					totalResources[resourceType] = totalQuantity
				} else {
					totalResources[resourceType] = replicaQuantity
				}
			}
		}
	}

	return totalResources
}

func (c *Controller) updateTFJobStatus(tfJob *tfjobv1.TFJob) error {
	if tfJob.Status.ReplicaStatuses == nil {
		tfJob.Status.ReplicaStatuses = map[commonv1.ReplicaType]*commonv1.ReplicaStatus{}
	}

	if tfJob.Status.Conditions == nil {
		tfJob.Status.Conditions = make([]commonv1.JobCondition, 0)
		tfJob.Status.Conditions = append(tfJob.Status.Conditions, commonv1.JobCondition{
			Type:           commonv1.JobConditionType(Queuing),
			Status:         corev1.ConditionTrue,
			LastUpdateTime: metav1.Now(),
		})

		_, err := c.tfjobClient.KubeflowV1().TFJobs(tfJob.Namespace).UpdateStatus(context.TODO(), tfJob, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("failed to update TFJob status: %v", err)
		}
		klog.Infof("Updated TFJob %v/%v status to Queuing", tfJob.Namespace, tfJob.Name)
	}

	return nil
}

func (c *Controller) deleteQueueUnitForTFJob(tfJob *tfjobv1.TFJob) {
	quName := tfJob.Name + QuNameSuffix
	qu, err := c.queueInformer.Lister().QueueUnits(tfJob.Namespace).Get(quName)
	if err != nil {
		if errors.IsNotFound(err) {
			klog.V(4).Infof("QueueUnit %v/%v not found for TFJob %v/%v", tfJob.Namespace, quName, tfJob.Namespace, tfJob.Name)
		} else {
			klog.Errorf("Failed to get QueueUnit %v/%v: %v", tfJob.Namespace, quName, err)
		}
		return
	}

	if qu.Spec.ConsumerRef.Name == tfJob.Name && qu.Spec.ConsumerRef.Kind == ConsumerRefKind {
		err = c.queueClient.SchedulingV1alpha1().QueueUnits(qu.Namespace).Delete(context.TODO(), qu.Name, metav1.DeleteOptions{})
		if err != nil {
			klog.Errorf("Failed to delete QueueUnit %v/%v: %v", qu.Namespace, qu.Name, err)
		} else {
			klog.Infof("Deleted QueueUnit %v/%v for TFJob %v/%v", qu.Namespace, qu.Name, tfJob.Namespace, tfJob.Name)
		}
	}
}

func (c *Controller) removeSuspendAnnotation(qu *v1alpha1.QueueUnit) error {
	namespace := qu.Spec.ConsumerRef.Namespace
	tfJobName := qu.Spec.ConsumerRef.Name

	tfJob, err := c.tfjobInformer.Lister().TFJobs(namespace).Get(tfJobName)
	if err != nil {
		if errors.IsNotFound(err) {
			klog.Warningf("TFJob %v/%v not found for QueueUnit %v/%v", namespace, tfJobName, qu.Namespace, qu.Name)
		}
		return err
	}

	// Create new annotations map without suspend annotation
	newAnnotations := make(map[string]string)
	for k, v := range tfJob.Annotations {
		if k != Suspend {
			newAnnotations[k] = v
		}
	}

	// Update TFJob annotations
	tfJobCopy := tfJob.DeepCopy()
	tfJobCopy.SetAnnotations(newAnnotations)

	err, retryable, _ := c.updateTFJobWithRetry(namespace, tfJobCopy)
	if retryable {
		// Retry with exponential backoff
		for retry := 0; retry < MaxUpdateRetries; retry++ {
			time.Sleep(RetryInterval * time.Duration(1<<retry))

			// Get latest version
			latestTFJob, getErr := c.tfjobInformer.Lister().TFJobs(namespace).Get(tfJobName)
			if getErr != nil {
				return getErr
			}

			// Update annotations on latest version
			latestCopy := latestTFJob.DeepCopy()
			latestCopy.SetAnnotations(newAnnotations)

			err, retryable, _ = c.updateTFJobWithRetry(namespace, latestCopy)
			if !retryable {
				break
			}

			if retry >= MaxUpdateRetries-1 {
				return fmt.Errorf("failed to update TFJob after %d retries: %v", MaxUpdateRetries, err)
			}
		}
	}

	if err != nil {
		return err
	}

	klog.Infof("Successfully removed suspend annotation from TFJob %v/%v", namespace, tfJobName)
	return nil
}

func (c *Controller) updateTFJobWithRetry(namespace string, tfJob *tfjobv1.TFJob) (error, bool, bool) {
	_, err := c.tfjobClient.KubeflowV1().TFJobs(namespace).Update(context.TODO(), tfJob, metav1.UpdateOptions{})
	if err != nil {
		if strings.Contains(err.Error(), OptimisticLockErrorMsg) {
			klog.V(4).Infof("Optimistic lock error when updating TFJob %v/%v", namespace, tfJob.Name)
			return err, true, false // retryable
		}
		klog.Errorf("Failed to update TFJob %v/%v: %v", namespace, tfJob.Name, err)
		return err, false, true // other error
	}
	return nil, false, false // success
}
