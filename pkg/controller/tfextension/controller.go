/*
 Copyright 2021 The Kube-Queue Authors.

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/

package tfextension

import (
	"fmt"
	"time"

	"k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/klog/v2"

	queueversioned "github.com/kube-queue/api/pkg/client/clientset/versioned"
	queueinformers "github.com/kube-queue/api/pkg/client/informers/externalversions/scheduling/v1alpha1"
	tfjobversioned "github.com/kubeflow/training-operator/pkg/client/clientset/versioned"
	tfjobinformers "github.com/kubeflow/training-operator/pkg/client/informers/externalversions/kubeflow.org/v1"
)

const (
	// MaxRetries is the number of times a tfextension item will be retried before it is dropped out of the queue.
	MaxRetries = 15
	// RetryInterval is the interval time when update tfjob failed
	RetryInterval = 200 * time.Millisecond
	// MaxUpdateRetries is the max retry times when update tfjob failed
	MaxUpdateRetries = 5
)

type Controller struct {
	kubeClient    *kubernetes.Clientset
	queueInformer queueinformers.QueueUnitInformer
	queueClient   *queueversioned.Clientset
	tfjobInformer tfjobinformers.TFJobInformer
	tfjobClient   *tfjobversioned.Clientset
	workqueue     workqueue.RateLimitingInterface
}

func NewController(
	kubeClient *kubernetes.Clientset,
	queueInformer queueinformers.QueueUnitInformer,
	queueClient *queueversioned.Clientset,
	tfjobInformer tfjobinformers.TFJobInformer,
	tfjobClient *tfjobversioned.Clientset,
	stopCh <-chan struct{}) (*Controller, error) {

	controller := &Controller{
		kubeClient:    kubeClient,
		queueInformer: queueInformer,
		queueClient:   queueClient,
		tfjobInformer: tfjobInformer,
		tfjobClient:   tfjobClient,
		workqueue:     workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "TFExtension"),
	}

	controller.addAllEventHandlers()

	return controller, nil
}

func (c *Controller) Run(workers int, stopCh <-chan struct{}) error {
	defer runtime.HandleCrash()
	defer c.workqueue.ShutDown()

	klog.Info("Starting TF Extension Controller")

	if !cache.WaitForCacheSync(stopCh, c.queueInformer.Informer().HasSynced, c.tfjobInformer.Informer().HasSynced) {
		return fmt.Errorf("failed to wait for caches to sync")
	}

	for i := 0; i < workers; i++ {
		go wait.Until(c.runWorker, time.Second, stopCh)
	}

	klog.Info("Started TF Extension Controller workers")
	<-stopCh
	klog.Info("Shutting down TF Extension Controller workers")

	return nil
}

func (c *Controller) runWorker() {
	for c.processNextWorkItem() {
	}
}

func (c *Controller) processNextWorkItem() bool {
	obj, shutdown := c.workqueue.Get()
	if shutdown {
		return false
	}

	err := func(obj interface{}) error {
		defer c.workqueue.Done(obj)
		var key string
		var ok bool

		if key, ok = obj.(string); !ok {
			c.workqueue.Forget(obj)
			runtime.HandleError(fmt.Errorf("expected string in workqueue but got %#v", obj))
			return nil
		}

		if err := c.syncHandler(key); err != nil {
			c.handleErr(err, key)
			return err
		}

		c.workqueue.Forget(obj)
		return nil
	}(obj)

	if err != nil {
		runtime.HandleError(err)
	}

	return true
}

func (c *Controller) handleErr(err error, key string) {
	if err == nil {
		c.workqueue.Forget(key)
		return
	}

	numRequeues := c.workqueue.NumRequeues(key)
	if numRequeues < MaxRetries {
		c.workqueue.AddRateLimited(key)
		klog.Infof("Retrying %v %d times, error: %v", key, numRequeues+1, err)
		return
	}

	runtime.HandleError(err)
	klog.Infof("Dropping item %q out of the workqueue: %v", key, err)
	c.workqueue.Forget(key)
}
